# 域名Whois信息收集器

这是一个Python脚本，用于获取指定域名的whois信息，并将结果保存在MongoDB中，支持并发执行。

## 功能特性

- 🚀 支持并发处理多个域名
- 📊 将whois信息存储到MongoDB
- 🔄 支持数据更新（upsert）
- 📝 详细的日志记录
- 🛡️ 完善的错误处理
- 📁 支持从文件批量读取域名

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 基本用法

```bash
# 查询单个域名
python whois_collector.py -d google.com

# 查询多个域名
python whois_collector.py -d google.com baidu.com github.com

# 从文件读取域名列表
python whois_collector.py -f domains_example.txt

# 组合使用
python whois_collector.py -d google.com -f domains_example.txt
```

### 2. 高级参数

```bash
# 自定义MongoDB连接
python whois_collector.py -d google.com --mongo-uri "*******************************************/"

# 自定义数据库和集合名称
python whois_collector.py -d google.com --db-name my_whois_db --collection my_domains

# 设置并发数
python whois_collector.py -f domains_example.txt --workers 20

# 强制更新模式（默认已会覆盖相同域名）
python whois_collector.py -d google.com --force
```

### 3. 查询和管理功能

```bash
# 查询现有域名记录
python whois_collector.py --query google.com

# 删除指定域名记录
python whois_collector.py --delete google.com
```

### 4. 参数说明

- `--domains, -d`: 域名列表，可以指定多个
- `--file, -f`: 包含域名的文件路径
- `--mongo-uri`: MongoDB连接URI（默认：mongodb://localhost:27017/）
- `--db-name`: 数据库名称（默认：whois_db）
- `--collection`: 集合名称（默认：domains）
- `--workers, -w`: 最大并发数（默认：10）
- `--force`: 强制更新已存在的域名记录（默认已会自动覆盖）
- `--query, -q`: 查询指定域名的现有记录
- `--delete`: 删除指定域名的记录

## 数据存储格式

数据以以下格式存储在MongoDB中：

```json
{
  "_id": ObjectId("..."),
  "domain": "example.com",
  "whois_info": {
    "domain_name": "EXAMPLE.COM",
    "registrar": "Example Registrar",
    "creation_date": "2023-01-01T00:00:00",
    "expiration_date": "2024-01-01T00:00:00",
    "name_servers": ["ns1.example.com", "ns2.example.com"],
    ...
  },
  "query_time": "2023-12-01T10:30:00",
  "status": "success"
}
```

## 域名文件格式

域名文件应该是纯文本文件，每行一个域名：

```
google.com
baidu.com
github.com
# 这是注释行，会被忽略
stackoverflow.com
```

## 日志

脚本会生成详细的日志，同时输出到控制台和文件（whois_collector.log）。

## 数据覆盖机制

⚠️ **重要**: 当查询相同域名时，脚本会**直接覆盖**MongoDB中的旧数据，包括：
- 完全替换整个文档
- 更新查询时间为最新时间
- 覆盖所有whois信息字段

这确保了数据库中始终保存最新的whois信息。

## 注意事项

1. 确保MongoDB服务正在运行
2. whois查询可能受到频率限制，建议适当控制并发数
3. 某些域名可能无法查询到whois信息
4. 脚本会自动处理重复域名（去重）
5. **相同域名会完全覆盖旧记录，无法恢复**

## 错误处理

- 网络错误：会记录错误并继续处理其他域名
- MongoDB连接错误：会终止程序
- whois查询失败：会记录错误信息到数据库

## 示例输出

### 首次查询域名
```
2023-12-01 10:30:00,123 - INFO - 成功连接到MongoDB: mongodb://localhost:27017/
📝 默认模式: 相同域名将自动覆盖旧记录
2023-12-01 10:30:00,124 - INFO - 开始并发处理 3 个域名，最大并发数: 10
2023-12-01 10:30:01,234 - INFO - 正在查询域名: google.com
2023-12-01 10:30:02,345 - INFO - 成功获取域名 google.com 的whois信息
2023-12-01 10:30:02,456 - INFO - 新增域名记录: google.com
2023-12-01 10:30:05,678 - INFO - 处理完成! 成功: 3, 失败: 0, 耗时: 5.55秒

📊 处理结果:
✅ 成功: 3
❌ 失败: 0
⏱️  耗时: 5.55秒

💾 数据已保存到MongoDB:
   数据库: whois_db
   集合: domains
```

### 重复查询相同域名（覆盖旧数据）
```
2023-12-01 11:00:01,234 - INFO - 正在查询域名: google.com
2023-12-01 11:00:02,345 - INFO - 成功获取域名 google.com 的whois信息
2023-12-01 11:00:02,456 - INFO - 覆盖域名记录: google.com (原记录查询时间: 2023-12-01T10:30:02)
```

### 查询现有记录
```bash
python whois_collector.py --query google.com
```
```
📋 域名 google.com 的记录:
查询时间: 2023-12-01T11:00:02
状态: success
Whois信息:
  domain_name: GOOGLE.COM
  registrar: MarkMonitor Inc.
  creation_date: 1997-09-15T04:00:00
  expiration_date: 2028-09-14T04:00:00
  ...
```
