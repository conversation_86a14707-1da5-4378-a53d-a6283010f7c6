#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试whois收集器的功能
"""

import unittest
from unittest.mock import patch, MagicMock
from whois_collector import WhoisCollector
import tempfile
import os


class TestWhoisCollector(unittest.TestCase):
    """测试WhoisCollector类"""
    
    def setUp(self):
        """测试前准备"""
        self.collector = WhoisCollector(
            mongo_uri="mongodb://localhost:27017/",
            db_name="test_whois_db",
            collection_name="test_domains",
            max_workers=2
        )
    
    def test_init(self):
        """测试初始化"""
        self.assertEqual(self.collector.db_name, "test_whois_db")
        self.assertEqual(self.collector.collection_name, "test_domains")
        self.assertEqual(self.collector.max_workers, 2)
    
    @patch('whois_collector.whois.whois')
    def test_get_whois_info_success(self, mock_whois):
        """测试成功获取whois信息"""
        # 模拟whois返回数据
        mock_result = MagicMock()
        mock_result.items.return_value = [
            ('domain_name', 'EXAMPLE.COM'),
            ('registrar', 'Test Registrar')
        ]
        mock_whois.return_value = mock_result
        
        result = self.collector.get_whois_info('example.com')
        
        self.assertIsNotNone(result)
        self.assertEqual(result['domain'], 'example.com')
        self.assertEqual(result['status'], 'success')
        self.assertIn('whois_info', result)
    
    @patch('whois_collector.whois.whois')
    def test_get_whois_info_failure(self, mock_whois):
        """测试获取whois信息失败"""
        mock_whois.side_effect = Exception("Network error")
        
        result = self.collector.get_whois_info('example.com')
        
        self.assertIsNotNone(result)
        self.assertEqual(result['domain'], 'example.com')
        self.assertEqual(result['status'], 'failed')
        self.assertIn('error', result)
    
    def test_load_domains_from_file(self):
        """测试从文件加载域名"""
        from whois_collector import load_domains_from_file
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(mode='w', delete=False, encoding='utf-8') as f:
            f.write("google.com\n")
            f.write("# 这是注释\n")
            f.write("baidu.com\n")
            f.write("\n")  # 空行
            temp_file = f.name
        
        try:
            domains = load_domains_from_file(temp_file)
            self.assertEqual(len(domains), 2)
            self.assertIn('google.com', domains)
            self.assertIn('baidu.com', domains)
        finally:
            os.unlink(temp_file)


def run_integration_test():
    """运行集成测试（需要MongoDB运行）"""
    print("运行集成测试...")
    
    collector = WhoisCollector(
        mongo_uri="mongodb://localhost:27017/",
        db_name="test_whois_db",
        collection_name="test_domains",
        max_workers=2
    )
    
    try:
        # 测试连接
        if not collector.connect_mongodb():
            print("❌ MongoDB连接失败，跳过集成测试")
            return
        
        print("✅ MongoDB连接成功")
        
        # 测试处理域名
        test_domains = ['example.com']
        result = collector.process_domains_concurrent(test_domains)
        
        print(f"✅ 处理结果: 成功={result['success']}, 失败={result['failed']}")
        
        # 清理测试数据
        collector.collection.delete_many({"domain": {"$in": test_domains}})
        print("✅ 清理测试数据完成")
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
    finally:
        collector.close()


if __name__ == "__main__":
    print("开始运行测试...")
    
    # 运行单元测试
    print("\n=== 单元测试 ===")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # 运行集成测试
    print("\n=== 集成测试 ===")
    run_integration_test()
    
    print("\n测试完成！")
