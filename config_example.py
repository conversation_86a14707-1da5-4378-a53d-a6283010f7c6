#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件示例
可以根据需要修改这些配置
"""

# MongoDB配置
MONGODB_CONFIG = {
    "uri": "mongodb://localhost:27017/",
    "database": "whois_db",
    "collection": "domains"
}

# 并发配置
CONCURRENT_CONFIG = {
    "max_workers": 10,  # 最大并发数
    "timeout": 30,      # 单个查询超时时间（秒）
}

# 日志配置
LOGGING_CONFIG = {
    "level": "INFO",
    "file": "whois_collector.log",
    "format": "%(asctime)s - %(levelname)s - %(message)s"
}

# 重试配置
RETRY_CONFIG = {
    "max_retries": 3,   # 最大重试次数
    "retry_delay": 1,   # 重试间隔（秒）
}

# 示例：如何使用配置文件
if __name__ == "__main__":
    from whois_collector import WhoisCollector
    
    collector = WhoisCollector(
        mongo_uri=MONGODB_CONFIG["uri"],
        db_name=MONGODB_CONFIG["database"],
        collection_name=MONGODB_CONFIG["collection"],
        max_workers=CONCURRENT_CONFIG["max_workers"]
    )
    
    # 处理示例域名
    domains = ["example.com", "test.com"]
    result = collector.process_domains_concurrent(domains)
    print(f"处理结果: {result}")
    collector.close()
