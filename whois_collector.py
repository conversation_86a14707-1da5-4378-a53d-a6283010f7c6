#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
域名Whois信息收集器
支持并发获取域名whois信息并存储到MongoDB
"""

import asyncio
import logging
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional
import whois
import pymongo
from pymongo import MongoClient
from pymongo.errors import DuplicateKeyError, PyMongoError
import argparse
import json
from datetime import datetime


class WhoisCollector:
    """Whois信息收集器"""
    
    def __init__(self, mongo_uri: str = "mongodb://localhost:27017/", 
                 db_name: str = "whois_db", 
                 collection_name: str = "domains",
                 max_workers: int = 10):
        """
        初始化收集器
        
        Args:
            mongo_uri: MongoDB连接URI
            db_name: 数据库名称
            collection_name: 集合名称
            max_workers: 最大并发数
        """
        self.mongo_uri = mongo_uri
        self.db_name = db_name
        self.collection_name = collection_name
        self.max_workers = max_workers
        self.client = None
        self.db = None
        self.collection = None
        
        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('whois_collector.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def connect_mongodb(self) -> bool:
        """连接MongoDB数据库"""
        try:
            self.client = MongoClient(self.mongo_uri)
            self.db = self.client[self.db_name]
            self.collection = self.db[self.collection_name]
            
            # 创建索引确保域名唯一
            self.collection.create_index("domain", unique=True)
            
            # 测试连接
            self.client.admin.command('ping')
            self.logger.info(f"成功连接到MongoDB: {self.mongo_uri}")
            return True
            
        except Exception as e:
            self.logger.error(f"连接MongoDB失败: {e}")
            return False
    
    def get_whois_info(self, domain: str) -> Optional[Dict]:
        """
        获取单个域名的whois信息
        
        Args:
            domain: 域名
            
        Returns:
            whois信息字典或None
        """
        try:
            self.logger.info(f"正在查询域名: {domain}")
            
            # 获取whois信息
            w = whois.whois(domain)
            
            if w is None:
                self.logger.warning(f"域名 {domain} 未找到whois信息")
                return None
            
            # 将whois对象转换为字典
            whois_data = {}
            for key, value in w.items():
                if value is not None:
                    # 处理日期类型
                    if isinstance(value, datetime):
                        whois_data[key] = value.isoformat()
                    elif isinstance(value, list):
                        # 处理列表中的日期
                        whois_data[key] = [
                            item.isoformat() if isinstance(item, datetime) else str(item)
                            for item in value
                        ]
                    else:
                        whois_data[key] = str(value)
            
            result = {
                "domain": domain.lower().strip(),
                "whois_info": whois_data,
                "query_time": datetime.now().isoformat(),
                "status": "success"
            }
            
            self.logger.info(f"成功获取域名 {domain} 的whois信息")
            return result
            
        except Exception as e:
            self.logger.error(f"获取域名 {domain} whois信息失败: {e}")
            return {
                "domain": domain.lower().strip(),
                "whois_info": {},
                "query_time": datetime.now().isoformat(),
                "status": "failed",
                "error": str(e)
            }
    
    def save_to_mongodb(self, whois_data: Dict) -> bool:
        """
        保存whois信息到MongoDB
        
        Args:
            whois_data: whois数据字典
            
        Returns:
            是否保存成功
        """
        try:
            # 使用upsert更新或插入
            result = self.collection.update_one(
                {"domain": whois_data["domain"]},
                {"$set": whois_data},
                upsert=True
            )
            
            if result.upserted_id:
                self.logger.info(f"新增域名记录: {whois_data['domain']}")
            else:
                self.logger.info(f"更新域名记录: {whois_data['domain']}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"保存域名 {whois_data['domain']} 到MongoDB失败: {e}")
            return False
    
    def process_single_domain(self, domain: str) -> bool:
        """
        处理单个域名的完整流程
        
        Args:
            domain: 域名
            
        Returns:
            是否处理成功
        """
        # 获取whois信息
        whois_data = self.get_whois_info(domain)
        
        if whois_data is None:
            return False
        
        # 保存到MongoDB
        return self.save_to_mongodb(whois_data)
    
    def process_domains_concurrent(self, domains: List[str]) -> Dict[str, int]:
        """
        并发处理多个域名
        
        Args:
            domains: 域名列表
            
        Returns:
            处理结果统计
        """
        if not self.connect_mongodb():
            return {"success": 0, "failed": len(domains)}
        
        success_count = 0
        failed_count = 0
        
        self.logger.info(f"开始并发处理 {len(domains)} 个域名，最大并发数: {self.max_workers}")
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_domain = {
                executor.submit(self.process_single_domain, domain): domain 
                for domain in domains
            }
            
            # 处理完成的任务
            for future in as_completed(future_to_domain):
                domain = future_to_domain[future]
                try:
                    success = future.result()
                    if success:
                        success_count += 1
                    else:
                        failed_count += 1
                except Exception as e:
                    self.logger.error(f"处理域名 {domain} 时发生异常: {e}")
                    failed_count += 1
        
        end_time = time.time()
        duration = end_time - start_time
        
        self.logger.info(f"处理完成! 成功: {success_count}, 失败: {failed_count}, 耗时: {duration:.2f}秒")
        
        return {
            "success": success_count,
            "failed": failed_count,
            "duration": duration
        }
    
    def close(self):
        """关闭数据库连接"""
        if self.client:
            self.client.close()
            self.logger.info("已关闭MongoDB连接")


def load_domains_from_file(file_path: str) -> List[str]:
    """从文件加载域名列表"""
    domains = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                domain = line.strip()
                if domain and not domain.startswith('#'):
                    domains.append(domain)
        return domains
    except Exception as e:
        logging.error(f"读取域名文件失败: {e}")
        return []


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='域名Whois信息收集器')
    parser.add_argument('--domains', '-d', nargs='+', help='域名列表')
    parser.add_argument('--file', '-f', help='包含域名的文件路径')
    parser.add_argument('--mongo-uri', default='mongodb://localhost:27017/', 
                       help='MongoDB连接URI')
    parser.add_argument('--db-name', default='whois_db', help='数据库名称')
    parser.add_argument('--collection', default='domains', help='集合名称')
    parser.add_argument('--workers', '-w', type=int, default=10, help='最大并发数')
    
    args = parser.parse_args()
    
    # 获取域名列表
    domains = []
    if args.domains:
        domains.extend(args.domains)
    if args.file:
        domains.extend(load_domains_from_file(args.file))
    
    if not domains:
        print("请提供域名列表或域名文件")
        return
    
    # 去重
    domains = list(set(domains))
    
    # 创建收集器并处理
    collector = WhoisCollector(
        mongo_uri=args.mongo_uri,
        db_name=args.db_name,
        collection_name=args.collection,
        max_workers=args.workers
    )
    
    try:
        result = collector.process_domains_concurrent(domains)
        print(f"\n处理结果:")
        print(f"成功: {result['success']}")
        print(f"失败: {result['failed']}")
        print(f"耗时: {result['duration']:.2f}秒")
    finally:
        collector.close()


if __name__ == "__main__":
    main()
